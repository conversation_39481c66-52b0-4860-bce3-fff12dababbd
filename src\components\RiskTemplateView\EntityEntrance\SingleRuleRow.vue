<script setup lang="tsx">
import type { RiskRule } from '../../../../../xtrade-sdk/dist';
import { RiskSummarizationMethod } from '../RiskParamTypeAndSummary';
import { isNotJson } from '@/script';

import {
  formatCheckObject,
  formatRuleDayRange,
  formatRuleTimeRange,
} from '../IndicatorTreeNodeLogic';

const { rules, focusedRuleId, componentMap } = defineProps<{
  rules: RiskRule[];
  focusedRuleId: number | null;
  componentMap: { [id: number]: string };
}>();

const emitter = defineEmits<{
  focused: [rule: RiskRule];
  edit: [rule: RiskRule];
}>();

function setAsFocused(rule: RiskRule) {
  emitter('focused', rule);
}

function formatIdcSummaries(rule: RiskRule) {
  const componentName = componentMap[rule.indicatorId];
  if (!componentName) {
    return ['[组件未注册]'];
  }

  const method = RiskSummarizationMethod[componentName];
  if (!method) {
    return ['[格式化函数缺失]'];
  }

  const { riskParam } = rule.configuration;
  if (isNotJson(riskParam) || JSON.stringify(riskParam) == '{}') {
    return ['{}'];
  }

  const summaries = method(riskParam);
  return Array.isArray(summaries) ? summaries : [summaries];
}

function editRow(rule: RiskRule) {
  emitter('edit', rule);
}

function unbindRow(rule: RiskRule) {
  console.log('unbind', rule.ruleName);
}
</script>

<template>
  <div class="rule-list" pt-5>
    <div
      v-for="(rule, index) in rules"
      :key="index"
      class="each-rule-row"
      :class="{ is_focused: rule.id == focusedRuleId }"
      @click="setAsFocused(rule)"
      flex
      aic
      lh-30
    >
      <div w-200 pl-15 toe>{{ rule.ruleName }}</div>
      <div flex-1 flex aic gap-10 of-x-hidden>
        <div w-100 flex-1>
          <div v-for="(info, info_idx) in formatIdcSummaries(rule)" :key="info_idx" w-full toe>
            {{ info }}
          </div>
        </div>
        <div>|</div>
        <div w-260 toe>针对{{ formatCheckObject(rule) }}</div>
        <div>|</div>
        <div w-150 toe>{{ formatRuleDayRange(rule) }}</div>
        <div>|</div>
        <div w-150 toe>{{ formatRuleTimeRange(rule) }} 执行</div>
      </div>
      <div w-90 pl-10 flex gap-10>
        <el-link @click="editRow(rule)" underline>编辑</el-link>
        <el-link @click="unbindRow(rule)" underline>解绑</el-link>
      </div>
    </div>
  </div>
</template>

<style scoped>
.rule-list {
  div {
    color: var(--g-text-color-2);
  }
  .each-rule-row {
    &:hover {
      background-color: var(--g-block-bg-6);
    }
    &.is_focused {
      background-color: var(--g-block-bg-6);
    }
  }
}
</style>
