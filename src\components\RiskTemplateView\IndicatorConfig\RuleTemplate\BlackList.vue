<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { AlertType, AlertTypes } from '@/enum/riskc';
import { deepClone } from '@/script';
import type { AnyIndicatorRiskParamObject } from '@/types/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';
import { RiskSummarizationMethod } from '../../RiskParamTypeAndSummary';

interface RuleInnerSetting extends AnyIndicatorRiskParamObject {
  alertType: number;
}

const { ruleSetting } = defineProps<{
  ruleSetting: RuleInnerSetting | null;
}>();

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>(createEmptyRiskParam());
const rules = {
  alertType: [{ required: true, message: '请选择对采取行动', trigger: 'blur' }],
};

watch(
  () => ruleSetting,
  newValue => {
    localRuleSetting.value = handleInputChange(newValue);
  },
  { immediate: true },
);

function handleInputChange(newValue: RuleInnerSetting | null) {
  return newValue && JSON.stringify(newValue) != '{}'
    ? deepClone(newValue)
    : createEmptyRiskParam();
}

function createEmptyRiskParam(): RuleInnerSetting {
  return {
    classType: IdcComponentNameDef.BlackList,
    alertType: AlertType.Warning.value,
  };
}

function validate() {
  return $form.value!.validate();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string | string[]];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  return RiskSummarizationMethod[IdcComponentNameDef.BlackList](localRuleSetting.value);
}

function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置" prop="alertType.value">
          <div w-full flex aic gap-10>
            <div w-auto>
              <label class="placed-label">黑名单</label>
            </div>
            <div flex-1>
              <el-select
                v-model="localRuleSetting.alertType"
                style="width: 140px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in AlertTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
