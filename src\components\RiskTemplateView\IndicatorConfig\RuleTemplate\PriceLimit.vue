<script setup lang="ts">
import { reactive, ref, useTemplateRef, watch } from 'vue';
import { deepClone } from '@/script';
import type { AnyIndicatorRiskParamObject } from '@/types/riskc';
import { RiskPriceType } from '@/enum/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';
import { RiskSummarizationMethod } from '../../RiskParamTypeAndSummary';

/**
 * 价格限制风控参数
 */
interface RuleInnerSetting extends AnyIndicatorRiskParamObject {
  marketPrice: number;
  upPrice: number;
  downPrice: number;
}

const { ruleSetting } = defineProps<{
  ruleSetting: RuleInnerSetting | null;
}>();

const { MarketPrice, CeilingPrice, FloorPrice } = RiskPriceType;
const checkboxs = reactive({
  marketPrice: true,
  upPrice: true,
  downPrice: true,
});

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>(createEmptyRiskParam());

const rules = {
  marketPrice: [{ required: true, message: '请选择', trigger: 'blur' }],
  upPrice: [{ required: true, message: '请选择', trigger: 'blur' }],
  downPrice: [{ required: true, message: '请选择', trigger: 'blur' }],
};

watch(
  () => ruleSetting,
  newValue => {
    if (newValue) {
      localRuleSetting.value = deepClone(newValue);
      const { marketPrice, upPrice, downPrice } = newValue;
      checkboxs.marketPrice = marketPrice == MarketPrice.value;
      checkboxs.upPrice = upPrice == CeilingPrice.value;
      checkboxs.downPrice = downPrice == FloorPrice.value;
    } else {
      localRuleSetting.value = createEmptyRiskParam();
    }
  },
  { immediate: true },
);

function createEmptyRiskParam(): RuleInnerSetting {
  return {
    classType: IdcComponentNameDef.PriceLimit,
    marketPrice: MarketPrice.value,
    upPrice: CeilingPrice.value,
    downPrice: FloorPrice.value,
  };
}

function validate() {
  return $form.value!.validate();
}

function changeHandler() {
  const obj = localRuleSetting.value;
  obj.marketPrice = checkboxs.marketPrice ? MarketPrice.value : 0;
  obj.upPrice = checkboxs.upPrice ? CeilingPrice.value : 0;
  obj.downPrice = checkboxs.downPrice ? FloorPrice.value : 0;
  handleParamHotChange();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string | string[]];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  return RiskSummarizationMethod[IdcComponentNameDef.PriceLimit](localRuleSetting.value);
}

function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置" prop="marketPrice">
          <el-checkbox v-model="checkboxs.marketPrice" @change="changeHandler">
            {{ RiskPriceType.MarketPrice.label }}
          </el-checkbox>
        </el-form-item>
        <el-form-item label="" prop="upPrice">
          <el-checkbox v-model="checkboxs.upPrice" @change="changeHandler">
            {{ RiskPriceType.CeilingPrice.label }}
          </el-checkbox>
        </el-form-item>
        <el-form-item label="" prop="downPrice">
          <el-checkbox v-model="checkboxs.downPrice" @change="changeHandler">
            {{ RiskPriceType.FloorPrice.label }}
          </el-checkbox>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
