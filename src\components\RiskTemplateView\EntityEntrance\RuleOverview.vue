<script lang="ts" setup>
import SingleRuleRow from './SingleRuleRow.vue';
import CommonSharedIndicator from '../IndicatorConfig/CommonSharedIndicator.vue';
import { computed, onMounted, ref } from 'vue';
import type { TreeInstance } from 'element-plus';
import { ElMessage, ElTree } from 'element-plus';
import { buildTree, isLeafNode, type Level1Node } from '../IndicatorTreeNodeLogic';
import { MockedFundTmpls } from './MockedFundTmpls';
import type { ContextualIndicatorInfo } from '@/types/riskc';
import { chooseConfirm, type ChooseConfirmDialogOption } from '@/script/interaction';
import { deepClone } from '@/script';

import {
  Repos,
  type EntityRiskTemplateInfo,
  type RiskIndicator,
  type RiskRule,
} from '../../../../../xtrade-sdk/dist';

const { productId, accountId } = defineProps<{
  productId?: number | string;
  accountId?: number | string;
}>();

const contextId = computed(() => {
  return (productId || accountId)!;
});

console.log('contextId', contextId.value);
const repoInstance = new Repos.RiskControlRepo();
const $treeRef = ref<TreeInstance>();
const treeNodes = ref<Level1Node[]>();
// 规则ID与其对应的VUE组件名称索引
const ruleId2ComponentNameMap = ref<{ [id: number]: string }>({});
// 当前主体（产品或账号）绑定的风控模板
const riskTemplates = ref<EntityRiskTemplateInfo[]>([]);
// 上层节点包含叶子节点的ID列表（排除叶子节点外的所有节点）
const parent2LeafNodeIdsMap = ref<{ [id: number]: number[] }>({});
// 叶子节点对应的规则列表
const leaf2RulesMap = ref<{ [id: number]: RiskRule[] }>({});
// 选中的规则ID
const focusedRuleId = ref<number | null>(null);

// 控制对话框显示
const dialogVisible = ref(false);
// 当前选中的规则
const selectedRule = ref<RiskRule | null>(null);
// 当前选中的指标信息
const selectedIndicator = ref<ContextualIndicatorInfo | null>(null);

const emitter = defineEmits<{
  dialog: [visible: boolean];
}>();

function doesParentContainSomeRules(level_id: number) {
  const leafIds = parent2LeafNodeIdsMap.value[level_id];
  if (!Array.isArray(leafIds) || leafIds.length === 0) {
    return false;
  }

  return leafIds.some(id => {
    const rules = leaf2RulesMap.value[id];
    return Array.isArray(rules) && rules.length > 0;
  });
}

function constructMaps(indicators: RiskIndicator[]) {
  // 初始化每个指标对应的组件名称
  const map0 = {} as any;
  indicators.forEach(item => {
    map0[item.id] = item.clientName;
  });

  ruleId2ComponentNameMap.value = map0;

  // 初始化上层节点包含叶子节点的ID列表
  const map1 = {} as any;
  indicators.forEach(item => {
    const { id, firstLevelCode: f, secondLevelCode: s } = item;
    (map1[f] || (map1[f] = [])).push(id);
    (map1[s] || (map1[s] = [])).push(id);
  });

  parent2LeafNodeIdsMap.value = map1;

  // 初始化每个指标对应规则列表
  const map2 = {} as any;
  indicators.forEach(item => {
    map2[item.id] = [];
  });

  leaf2RulesMap.value = map2;
}

async function requestIdcTree() {
  const indicators = (await repoInstance.QueryIndicators()).data || [];
  treeNodes.value = buildTree(indicators);
  constructMaps(indicators);
}

async function requestAttachedTmpls() {
  // const list = (await repoInstance.QueryProductTemplates(contextId.value)).data || [];
  const list = MockedFundTmpls as any[] as EntityRiskTemplateInfo[];
  riskTemplates.value = list;
  const riskRules = list.map(item => item.ruleList).flat();
  riskRules.forEach(item => {
    leaf2RulesMap.value[item.indicatorId].push(item);
  });
}

function clearUnconcernedNodes() {
  // 找出没有任何规则的叶子节点
  const emptyLeafNodes: number[] = [];
  for (const id in leaf2RulesMap.value) {
    if (leaf2RulesMap.value[id].length === 0) {
      emptyLeafNodes.push(Number(id));
    }
  }

  // 所有叶子节点均包含规则
  if (emptyLeafNodes.length == 0) {
    return;
  }

  const allNodes = treeNodes.value as Level1Node[];

  /*
   * 从树中移除：
   * 1. 不包含规则的叶子节点
   * 2. 不包含任何规则的父节点
   */

  if (allNodes) {
    // 遍历并过滤第一层节点
    for (let i = allNodes.length - 1; i >= 0; i--) {
      const lv1Node = allNodes[i];
      const lv2Nodes = lv1Node.children;

      // 遍历并过滤第二层节点
      for (let j = lv2Nodes.length - 1; j >= 0; j--) {
        const lv2Node = lv2Nodes[j];

        // 过滤掉没有规则的叶子节点
        lv2Node.children = lv2Node.children.filter(
          leafNode => !emptyLeafNodes.includes(leafNode.id),
        );

        // 如果第二层节点没有子节点了，则移除该第二层节点
        if (lv2Node.children.length === 0) {
          lv2Nodes.splice(j, 1);
        }
      }

      // 如果第一层节点没有子节点了，则移除该第一层节点
      if (lv2Nodes.length === 0) {
        allNodes.splice(i, 1);
      }
    }
  }

  // 更新 parent2LeafNodeIdsMap，移除已删除的叶子节点
  Object.keys(parent2LeafNodeIdsMap.value).forEach(parentId => {
    const key = parentId as unknown as number;
    parent2LeafNodeIdsMap.value[key] = parent2LeafNodeIdsMap.value[key].filter(
      (nodeId: number) => !emptyLeafNodes.includes(nodeId),
    );
  });

  // 移除 leaf2RulesMap 中已删除的叶子节点
  emptyLeafNodes.forEach(nodeId => {
    delete leaf2RulesMap.value[nodeId];
  });
}

async function request() {
  await requestIdcTree();
  await requestAttachedTmpls();
  clearUnconcernedNodes();
}

// 处理规则行点击
function handleFocused(rule: RiskRule) {
  focusedRuleId.value = rule.id;
}

function handleEditRule(rule: RiskRule) {
  selectedRule.value = deepClone(rule);
  // 构造指标信息对象
  selectedIndicator.value = {
    indicatorId: rule.indicatorId,
    indicatorName: '', // 这里需要根据实际指标信息填充
    componentName: ruleId2ComponentNameMap.value[rule.indicatorId] || '',
  };
  dialogVisible.value = true;
  emitter('dialog', true);
}

async function ask4Choice() {
  const confirmButtonText = '更新此模版，同时更新关联产品的风控设置';
  const cancelButtonText = '解绑模版，只更新当前产品';
  const title = '修改风控规则确认';
  const message = `此模板绑定了其他产品，如仅修改当前产品，请点击 “${cancelButtonText}” ？`;
  const options: ChooseConfirmDialogOption = { confirmButtonText, cancelButtonText };
  const result = await chooseConfirm(title, message, options);
  return result;
}

async function handleRuleSubmit(rule: RiskRule) {
  handleCloseDialog();
  rule.id = null as any;
  const result = await ask4Choice();

  if (result == 'confirm') {
    // 直接重写原来的规则
    const resp = await repoInstance.UpdateRule(rule);
    const { errorCode, errorMsg } = resp;

    if (errorCode == 0) {
      ElMessage.success('保存成功');
      request();
    } else {
      ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
    }
  } else if (result == 'cancel') {
    ElMessage.error('解绑再绑定功能暂未实现');
  } else {
    ElMessage.info('已放弃修改');
  }
}

// 关闭对话框
function handleCloseDialog() {
  dialogVisible.value = false;
  selectedRule.value = null;
  selectedIndicator.value = null;
  emitter('dialog', false);
}

onMounted(() => {
  request();
});
</script>

<template>
  <div class="tree-control" h-500 p-10 of-y-auto>
    <!-- 树形结构 -->
    <el-tree
      ref="$treeRef"
      empty-text="无指标数据"
      node-key="id"
      :props="{ label: 'name', children: 'children' }"
      :data="treeNodes"
      :show-checkbox="false"
      highlight-current
      default-expand-all
    >
      <template #default="{ data }">
        <div
          v-if="isLeafNode(data)"
          class="leaf-node"
          :class="{ is_invisible: leaf2RulesMap[data.id].length == 0 }"
          w-full
          of-x-hidden
        >
          <div class="leaf-name">{{ data.name }}</div>
          <template v-if="leaf2RulesMap[data.id].length > 0">
            <div class="idc-rule-box">
              <SingleRuleRow
                :rules="leaf2RulesMap[data.id]"
                :focused-rule-id="focusedRuleId"
                :component-map="ruleId2ComponentNameMap"
                @focused="handleFocused"
                @edit="handleEditRule"
              ></SingleRuleRow>
            </div>
          </template>
        </div>
        <div
          v-else
          class="parent-node"
          :class="{ is_invisible: !doesParentContainSomeRules(data.id) }"
        >
          {{ data.name }}
        </div>
      </template>
    </el-tree>
  </div>

  <!-- 风控规则修改对话框 -->

  <el-dialog v-model="dialogVisible" title="风控指标配置" width="800px" @close="handleCloseDialog">
    <CommonSharedIndicator
      v-if="selectedRule && selectedIndicator"
      :context-rule="selectedRule"
      :context-indicator="selectedIndicator"
      :is-vendor="true"
      @submit="handleRuleSubmit"
      append-to-body
    />
  </el-dialog>
</template>

<style scoped>
.tree-control {
  :deep() {
    .el-tree-node__content {
      height: unset !important;
      cursor: default !important;
    }

    .el-tree-node__label {
      font-size: 14px;
      font-weight: 400;
      color: var(--g-text-color-2);
    }

    .el-tree-node__content:hover {
      background-color: unset !important;
    }

    .el-tree-node {
      &.is-current {
        > .el-tree-node__content {
          background-color: unset !important;
        }
      }
    }

    .el-tree-node__content {
      display: flex;
      align-items: center;
    }
  }
}
</style>
