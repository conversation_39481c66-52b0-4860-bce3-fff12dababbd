import { enumToArray } from '@/script';

/**
 * 触发风控阈值比较符
 */
export enum RiskTriggerComparer {
  '>' = 1,
  '<' = 2,
}

/**
 * 触发风控阈值比较符
 */
export const RISK_TRIGGER_COMPARERS = enumToArray(RiskTriggerComparer);

/**
 * 风控预警类型常量
 */
export const AlertType = {
  Ignore: { label: '忽略', value: 0 },
  Warning: { label: '预警', value: 1 },
  Prevention: { label: '禁止下单', value: 2 },
  ForbiddenOfCancel: { label: '禁止撤单', value: 3 },
};

/**
 * 触发风控采取动作
 */
export const AlertTypes = Object.values(AlertType);

/**
 * 风控表达式类型常量
 */
export const ExpressionType = {
  GreaterThan: { label: '大于', value: 1 },
  GreaterEqual: { label: '大于等于', value: 2 },
  Equal: { label: '等于', value: 3 },
  LessThan: { label: '小于', value: 4 },
  LessEqual: { label: '小于等于', value: 5 },
};

/**
 * 触发风控采取动作
 */
export const ExpressionTypes = Object.values(ExpressionType);

/**
 * 风控环节控制
 */
export const RiskStepControl = {
  instruction: { label: '指令', value: 1 },
  entrusting: { label: '委托', value: 2 },
  progressing: { label: '事中', value: 4 },
  marketClosed: { label: '盘后', value: 8 },
};

/**
 * 风控环节控制
 */
export const RiskStepControls = Object.values(RiskStepControl);

/**
 * 价格偏离类型
 */
export const PriceDeviationType = {
  LatestPrice: { label: '限制与最新价偏离', value: 1 },
  YesterdayClose: { label: '限制与昨收盘价偏离', value: 2 },
};

/**
 * 价格偏离类型
 */
export const PriceDeviationTypes = Object.values(PriceDeviationType);

/**
 * 风控管控交易方向
 */
export const RiskBsFlag = {
  Buy: { label: '买入', value: 1 },
  Sell: { label: '卖出', value: 2 },
  Unset: { label: '不限制', value: 3 },
};

/**
 * 风控管控交易方向
 */
export const RiskBsFlags = Object.values(RiskBsFlag);

/**
 * 风控价格类型
 */
export const RiskPriceType = {
  MarketPrice: { label: '限制市价', value: 1 },
  CeilingPrice: { label: '限制涨停价', value: 2 },
  FloorPrice: { label: '限制跌停价', value: 4 },
};

/**
 * 风控价格类型
 */
export const RiskPriceTypes = Object.values(RiskPriceType);

/**
 * 风控统计类型
 */
export const RiskStatisticsType = {
  SingleInstrument: { label: '单票', value: 1 },
  Summary: { label: '汇总', value: 2 },
};

/**
 * 风控统计类型
 */
export const RiskStatisticsTypes = Object.values(RiskStatisticsType);

/**
 * 市值类型
 */
export const MarketValueType = {
  MARKET_VALUE: { label: '市值', value: 1 },
  LONG_MARKET_VALUE: { label: '多头市值', value: 2 },
  SHORT_MARKET_VALUE: { label: '空头市值', value: 3 },
  NETTING_MARKET_VALUE: { label: '轧差市值', value: 4 },
};

/**
 * 市值类型
 */
export const MarketValueTypes = Object.values(MarketValueType);

/**
 * 市值占比类型
 */
export const MarketValueRatioType = {
  MARKET_VALUE: { label: '资产市值/产品市值', value: 1 },
  LONG_MARKET_VALUE: { label: '多头市值/产品市值', value: 2 },
  SHORT_MARKET_VALUE: { label: '空头市值/产品市值', value: 3 },
  NETTING_MARKET_VALUE: { label: '轧差市值/产品市值', value: 4 },
};

/**
 * 市值占比类型
 */
export const MarketValueRatioTypes = Object.values(MarketValueRatioType);

/**
 * 期货保证金占比类型
 */
export const FuturesMarginRatioType = {
  MARKET_VALUE: { label: '期货保证金/期货账户总资产', value: 1 },
  LONG_MARKET_VALUE: { label: '多头市值/期货账户总资产', value: 2 },
  SHORT_MARKET_VALUE: { label: '空头市值/期货账户总资产', value: 3 },
  NETTING_MARKET_VALUE: { label: '轧差市值/期货账户总资产', value: 4 },
};

/**
 * 期货保证金占比类型
 */
export const FuturesMarginRatioTypes = Object.values(FuturesMarginRatioType);

/**
 * 净值止损类型
 */
export const NavValueStopLossType = {
  WARNING_LINE: { label: '预警线', value: 1 },
  STOP_LOSS_LINE: { label: '止损线', value: 2 },
  SPECIFIED_VALUE: { label: '指定值', value: 3 },
};

/**
 * 净值止损类型
 */
export const NavValueStopLossTypes = Object.values(NavValueStopLossType);

/**
 * 动态条件类型
 */
export const DynamicConditionType = {
  Flowable_Market_Value: {
    label: '股票流通市值（亿）',
    fmt: '股票流通市值{0}{1}亿',
    value: 1,
    variable: 's_dq_mv',
    min: 0,
    max: 999999999999,
  },
  Pledge_Rate: {
    label: '股票质押比率（0-100%）',
    fmt: '股票质押比率{0}{1}%的股票',
    value: 2,
    variable: 's_pledge_ratio',
    min: 0,
    max: 100,
  },
  Onboard_Not_More_than: {
    label: '上市未满月数',
    fmt: '上市{0}{1}个月的股票',
    value: 3,
    variable: 's_info_listdate',
    min: 0,
    max: 12 * 40,
  },
  Avg_Day_Trade_Amount: {
    label: '年日均成交量（亿）',
    fmt: '年日均{0}{1}万的主板、中小板',
    value: 4,
    variable: 's_mq_amount',
    min: 0,
    max: 999999999999,
  },
};

/**
 * 动态条件类型
 */
export const DynamicConditionTypes = Object.values(DynamicConditionType);
