export const MockedFundTmpls = [
  {
    ruleList: [
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 3,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757385719000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757385720000,
        indicatorId: 3,
        orgId: 2,
        ruleName: '单笔最大委托数量_250909',
        templateId: 115071669567488,
        updateTime: 1757385719000,
      },
      {
        active: true,
        beginDay: 20250908,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [
            {
              expression: 2,
              id: 1757313069000,
              name: 's_mq_amount',
              value: 50,
            },
          ],
          indicatorId: 2,
          kindCodes: ['k_10003', 'k_10006', 'k_50001', 'k_20006', 'k_20007', 'k_20004', 'k_10001'],
          riskParam: {},
        },
        createTime: 1757298862000,
        createUserId: 1,
        endDay: 20250908,
        endTime: 150000,
        id: 1757298878000,
        indicatorId: 2,
        orgId: 1,
        ruleName: '白名单_250908_103421',
        templateId: 115071669567488,
        updateTime: 1757385352000,
      },
      {
        active: true,
        beginDay: 20250908,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 3,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757321281000,
        createUserId: 0,
        endDay: 20250908,
        endTime: 150000,
        id: 1757321298000,
        indicatorId: 3,
        orgId: 0,
        ruleName: '单笔最大委托数量_250908_164801',
        templateId: 115071669567488,
        updateTime: 1757321281000,
      },
      {
        active: false,
        beginDay: 0,
        beginTime: 93000,
        checkInterval: 100,
        checkObject: 7,
        configuration: {
          baseConditions: [],
          indicatorId: 1,
          kindCodes: ['k_10001'],
          riskParam: {},
        },
        createTime: 1757321474000,
        createUserId: 1,
        endDay: 20250908,
        endTime: 150000,
        id: 1757321482000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_250908_165114',
        templateId: 115071669567488,
        updateTime: 1757385070000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 2,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757387103000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757387107000,
        indicatorId: 2,
        orgId: 2,
        ruleName: '白名单_250909_110502',
        templateId: 115071669567488,
        updateTime: 1757387103000,
      },
      {
        active: true,
        beginDay: 0,
        beginTime: 0,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 6,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757386341000,
        createUserId: 1,
        endDay: 0,
        endTime: 0,
        id: 1757386345000,
        indicatorId: 6,
        orgId: 2,
        ruleName: '净买入金额_250909_105220',
        templateId: 115071669567488,
        updateTime: 1757386341000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [
            {
              expression: 2,
              id: 1757313069000,
              name: 's_mq_amount',
              value: 5000,
            },
          ],
          indicatorId: 2,
          kindCodes: ['k_10015', 'k_50001', 'k_20006', 'k_20007', 'k_20004', 'k_20005', 'k_10012'],
          riskParam: {},
        },
        createTime: 1757383134000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757383163000,
        indicatorId: 2,
        orgId: 2,
        ruleName: '白名单_250909_095853',
        templateId: 115071669567488,
        updateTime: 1757383134000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 22,
          kindCodes: ['k_50001', 'k_10001'],
          riskParam: {},
        },
        createTime: 1757399326000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757399364000,
        indicatorId: 22,
        orgId: 2,
        ruleName: '市值占比_250909_142845',
        templateId: 115071669567488,
        updateTime: 1757399326000,
      },
      {
        active: true,
        beginDay: 20250908,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 4,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757301510000,
        createUserId: 1,
        endDay: 20250908,
        endTime: 150000,
        id: 1757301511000,
        indicatorId: 4,
        orgId: 2,
        ruleName: '账户总委托数量_250909_01',
        templateId: 115071669567488,
        updateTime: 1757385070000,
      },
      {
        active: true,
        beginDay: 20250908,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [],
          indicatorId: 2,
          kindCodes: ['k_10003', 'k_20006'],
          riskParam: {},
        },
        createTime: 1757298769000,
        createUserId: 1,
        endDay: 20250908,
        endTime: 150000,
        id: 1757298820000,
        indicatorId: 2,
        orgId: 1,
        ruleName: '白名单_250908_103249_test',
        templateId: 115071669567488,
        updateTime: 1757385352000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 1,
        configuration: {
          baseConditions: [],
          indicatorId: 2,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757387113000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757387116000,
        indicatorId: 2,
        orgId: 2,
        ruleName: '白名单_250909_110513',
        templateId: 115071669567488,
        updateTime: 1757387113000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [
            {
              expression: 2,
              id: 1757313069000,
              name: 's_mq_amount',
              value: 5000,
            },
          ],
          indicatorId: 2,
          kindCodes: [
            'k_20001',
            'k_50001',
            'k_20006',
            'k_50002',
            'k_20007',
            'k_20004',
            'k_30001',
            'k_20005',
            'k_10001',
            'k_20002',
            'k_20003',
          ],
          riskParam: {},
        },
        createTime: 1757387122000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757387124000,
        indicatorId: 2,
        orgId: 2,
        ruleName: '白名单_250909_110521',
        templateId: 115071669567488,
        updateTime: 1757387122000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 7,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757396486000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757396502000,
        indicatorId: 7,
        orgId: 2,
        ruleName: '交易频率控制_250909_134125',
        templateId: 115071669567488,
        updateTime: 1757396486000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 29,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757386271000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757386276000,
        indicatorId: 29,
        orgId: 2,
        ruleName: '自成交挂单test1',
        templateId: 115071669567488,
        updateTime: 1757386271000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [],
          indicatorId: 23,
          kindCodes: [
            'k_10003',
            'k_10005',
            'k_10006',
            'k_20006',
            'k_20007',
            'k_20004',
            'k_20005',
            'k_10002',
          ],
          riskParam: {},
        },
        createTime: 1757399372000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757399401000,
        indicatorId: 23,
        orgId: 2,
        ruleName: '总股本占比_250909_142931',
        templateId: 115071669567488,
        updateTime: 1757399372000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 29,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757387453000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757387454000,
        indicatorId: 29,
        orgId: 2,
        ruleName: '自成交挂单_250909_111052',
        templateId: 115071669567488,
        updateTime: 1757387453000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 4,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757386455000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757386455000,
        indicatorId: 4,
        orgId: 2,
        ruleName: '账户总委托数量_250909_105415',
        templateId: 115071669567488,
        updateTime: 1757386455000,
      },
      {
        active: true,
        beginDay: 0,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 9,
        configuration: {
          baseConditions: [],
          indicatorId: 1,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757321395000,
        createUserId: 1,
        endDay: 0,
        endTime: 150000,
        id: 1757321427000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_250908_164954',
        templateId: 115071669567488,
        updateTime: 1757385070000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 3,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757383811000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757383816000,
        indicatorId: 3,
        orgId: 2,
        ruleName: '单笔最大委托数量_250909',
        templateId: 115071669567488,
        updateTime: 1757383811000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 3,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757383342000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757383397000,
        indicatorId: 3,
        orgId: 2,
        ruleName: '单笔最大委托数量_250909_100222-999',
        templateId: 115071669567488,
        updateTime: 1757385186000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 5,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757388469000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757388470000,
        indicatorId: 5,
        orgId: 2,
        ruleName: '单笔最大交易额_250909_112748',
        templateId: 115071669567488,
        updateTime: 1757388469000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 9,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757385728000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757385729000,
        indicatorId: 9,
        orgId: 2,
        ruleName: '废单比率限制_250909_104208',
        templateId: 115071669567488,
        updateTime: 1757385728000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 4,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757395516000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757395704000,
        indicatorId: 4,
        orgId: 2,
        ruleName: '账户总委托数量_250909_132515',
        templateId: 115071669567488,
        updateTime: 1757395516000,
      },
      {
        active: true,
        beginDay: 0,
        beginTime: 93000,
        checkInterval: 66,
        checkObject: 15,
        configuration: {
          baseConditions: [
            {
              expression: 4,
              id: 0,
              name: 'pledage_rate',
              value: 10,
            },
            {
              expression: 4,
              id: 0,
              name: 's_info_listdate',
              value: 34,
            },
          ],
          indicatorId: 0,
          kindCodes: [
            'k_10004',
            'k_20001',
            'k_10008',
            'k_50000',
            'k_50001',
            'k_20007',
            'k_10000',
            'k_30002',
          ],
        },
        createTime: 1757237547000,
        createUserId: 1,
        endDay: 0,
        endTime: 150000,
        id: 1757238019000,
        indicatorId: 1,
        orgId: 1,
        ruleName: '黑名单_9999',
        templateId: 115071669567488,
        updateTime: 1757385352000,
      },
      {
        active: true,
        beginDay: 20250908,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [
            {
              expression: 4,
              id: 1757232029000,
              name: 'pledage_rate',
              value: 10,
            },
          ],
          indicatorId: 1,
          kindCodes: ['k_10003', 'k_10005', 'k_10008', 'k_10009', 'k_10000', 'k_10001', 'k_10002'],
          riskParam: {},
        },
        createTime: 1757318790000,
        createUserId: 1,
        endDay: 20250908,
        endTime: 150000,
        id: 1757318793000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_ddddddddd',
        templateId: 115071669567488,
        updateTime: 1757385070000,
      },
      {
        active: true,
        beginDay: 20250908,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 1,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757321395000,
        createUserId: 1,
        endDay: 20250908,
        endTime: 150000,
        id: 1757321422000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_250908_164954',
        templateId: 115071669567488,
        updateTime: 1757385070000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 21,
          kindCodes: ['k_10001'],
          riskParam: {},
        },
        createTime: 1757399085000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757399218000,
        indicatorId: 21,
        orgId: 2,
        ruleName: '市值_250909_142445',
        templateId: 115071669567488,
        updateTime: 1757399085000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 29,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757386523000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757386522000,
        indicatorId: 29,
        orgId: 2,
        ruleName: '自成交挂单_250909_105523',
        templateId: 115071669567488,
        updateTime: 1757386523000,
      },
      {
        active: false,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 7,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757396217000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757396218000,
        indicatorId: 7,
        orgId: 2,
        ruleName: '交易频率控制_250909_133657',
        templateId: 115071669567488,
        updateTime: 1757396217000,
      },
      {
        active: true,
        beginDay: 0,
        beginTime: 0,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 5,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757396009000,
        createUserId: 1,
        endDay: 0,
        endTime: 0,
        id: 1757396015000,
        indicatorId: 5,
        orgId: 2,
        ruleName: '单笔最大交易额_250909_133328',
        templateId: 115071669567488,
        updateTime: 1757396009000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 160,
        checkObject: 15,
        configuration: {
          baseConditions: [
            {
              expression: 4,
              id: 1757232029000,
              name: 'pledage_rate',
              value: 10,
            },
            {
              expression: 4,
              id: 1757232536000,
              name: 's_info_listdate',
              value: 3,
            },
          ],
          indicatorId: 1,
          kindCodes: ['k_40002', 'k_40003', 'k_40000', 'k_40001', 'k_40004', 'k_40005'],
          riskParam: {},
        },
        createTime: 1757381367000,
        createUserId: 1,
        endDay: 0,
        endTime: 150000,
        id: 1757381395000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_250909_092926',
        templateId: 115071669567488,
        updateTime: 1757385070000,
      },
      {
        active: true,
        beginDay: 0,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 3,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757385505000,
        createUserId: 0,
        endDay: 20250909,
        endTime: 150000,
        id: 1757385510000,
        indicatorId: 3,
        orgId: 0,
        ruleName: '单笔最大委托数量_250909_103824',
        templateId: 115071669567488,
        updateTime: 1757385505000,
      },
    ],
    templateId: 115071669567488,
  },
  {
    ruleList: [
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [
            {
              expression: 4,
              id: 1757232536000,
              name: 's_info_listdate',
              value: 3,
            },
          ],
          indicatorId: 1,
          kindCodes: ['k_30003'],
          riskParam: {},
        },
        createTime: 1757396941000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757396949000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_250909_xwe23',
        templateId: 115071843893248,
        updateTime: 1757396941000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 6,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757397028000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757397041000,
        indicatorId: 6,
        orgId: 2,
        ruleName: '净买入金额_ggg',
        templateId: 115071843893248,
        updateTime: 1757397028000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 6,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757397044000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757397049000,
        indicatorId: 6,
        orgId: 2,
        ruleName: '净买入金额_250909_56666',
        templateId: 115071843893248,
        updateTime: 1757397044000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [
            {
              expression: 4,
              id: 1757232029000,
              name: 'pledage_rate',
              value: 10,
            },
            {
              expression: 4,
              id: 1757232536000,
              name: 's_info_listdate',
              value: 3,
            },
          ],
          indicatorId: 1,
          kindCodes: [
            'k_10003',
            'k_10014',
            'k_10005',
            'k_10006',
            'k_50000',
            'k_30002',
            'k_10012',
            'k_30003',
          ],
          riskParam: {},
        },
        createTime: 1757396953000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757396958000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_250909_QQQ',
        templateId: 115071843893248,
        updateTime: 1757396953000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 5,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757396963000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757396970000,
        indicatorId: 5,
        orgId: 2,
        ruleName: '单笔最大交易额_250909_AAA',
        templateId: 115071843893248,
        updateTime: 1757396963000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [],
          indicatorId: 1,
          kindCodes: ['k_20005', 'k_20002'],
          riskParam: {},
        },
        createTime: 1757396906000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757396926000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_0909_569rc',
        templateId: 115071843893248,
        updateTime: 1757396906000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 9,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757397057000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757397060000,
        indicatorId: 9,
        orgId: 2,
        ruleName: '废单比率限制_250909_5454',
        templateId: 115071843893248,
        updateTime: 1757397057000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 5,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757397052000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757397054000,
        indicatorId: 5,
        orgId: 2,
        ruleName: '单笔最大交易额_250909_2353252',
        templateId: 115071843893248,
        updateTime: 1757397052000,
      },
    ],
    templateId: 115071843893248,
  },
  {
    ruleList: [],
    templateId: 1757401866000,
  },
  {
    ruleList: [
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [
            {
              expression: 2,
              id: 1757313069000,
              name: 's_mq_amount',
              value: 5000,
            },
          ],
          indicatorId: 1,
          kindCodes: ['k_20001', 'k_20002'],
          riskParam: {},
        },
        createTime: 1757398715000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757398721000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_250909_2352',
        templateId: 115071610454016,
        updateTime: 1757398715000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 28,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757409701000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757409712000,
        indicatorId: 28,
        orgId: 2,
        ruleName: '日内反向_11111',
        templateId: 115071610454016,
        updateTime: 1757409701000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [],
          indicatorId: 1,
          kindCodes: ['k_20001', 'k_20005'],
          riskParam: {},
        },
        createTime: 1757398651000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757398666000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_mmmm',
        templateId: 115071610454016,
        updateTime: 1757398651000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 5,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757398672000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757398678000,
        indicatorId: 5,
        orgId: 2,
        ruleName: '单笔最大交易额_新的地方',
        templateId: 115071610454016,
        updateTime: 1757398672000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 28,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757409733000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757409775000,
        indicatorId: 28,
        orgId: 2,
        ruleName: '日内反向_250909_172213',
        templateId: 115071610454016,
        updateTime: 1757409733000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 4,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757398695000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757398700000,
        indicatorId: 4,
        orgId: 2,
        ruleName: '账户总委托数量_3453',
        templateId: 115071610454016,
        updateTime: 1757398695000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 5,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757398707000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757398712000,
        indicatorId: 5,
        orgId: 2,
        ruleName: '单笔最大交易额_250909_向舒适性',
        templateId: 115071610454016,
        updateTime: 1757398707000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 15,
        configuration: {
          baseConditions: [
            {
              expression: 4,
              id: 1757232536000,
              name: 's_info_listdate',
              value: 3,
            },
          ],
          indicatorId: 1,
          kindCodes: ['k_40002', 'k_40005'],
          riskParam: {},
        },
        createTime: 1757398723000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757398728000,
        indicatorId: 1,
        orgId: 2,
        ruleName: '黑名单_250909_则沃尔沃',
        templateId: 115071610454016,
        updateTime: 1757398723000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 4,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757398681000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757398685000,
        indicatorId: 4,
        orgId: 2,
        ruleName: '账户总委托数量_250909_243给34',
        templateId: 115071610454016,
        updateTime: 1757398681000,
      },
      {
        active: true,
        beginDay: 20250909,
        beginTime: 93000,
        checkInterval: 60,
        checkObject: 3,
        configuration: {
          baseConditions: [],
          indicatorId: 3,
          kindCodes: [],
          riskParam: {},
        },
        createTime: 1757398688000,
        createUserId: 1,
        endDay: 20250909,
        endTime: 150000,
        id: 1757398691000,
        indicatorId: 3,
        orgId: 2,
        ruleName: '单笔最大委托数量_250909_454545',
        templateId: 115071610454016,
        updateTime: 1757398688000,
      },
    ],
    templateId: 115071610454016,
  },
];
